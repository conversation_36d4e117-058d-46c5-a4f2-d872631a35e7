package sui

import (
	"context"
	"sync"

	"github.com/block-vision/sui-go-sdk/constant"
	"github.com/block-vision/sui-go-sdk/models"
	"github.com/block-vision/sui-go-sdk/sui"
	"github.com/sirupsen/logrus"
)

type SuiRpcClient struct {
	client sui.ISuiAPI
}

var (
	suiClientInstance *SuiRpcClient
	suiClientOnce     sync.Once
)

// NewSuiClient 创建一个新的SuiRpcClient实例
func NewSuiClient() *SuiRpcClient {
	suiClientOnce.Do(func() {
		cli := sui.NewSuiClient(constant.BvMainnetEndpoint)
		suiClientInstance = &SuiRpcClient{
			client: cli,
		}
	})
	return suiClientInstance
}

func (s *SuiRpcClient) GetClient() sui.ISuiAPI {
	return s.client
}

func (s *SuiRpcClient) SuiGetLatestCheckpointSequenceNumber(ctx context.Context) (uint64, error) {
	latestCheckpoint, err := s.client.SuiGetLatestCheckpointSequenceNumber(ctx)
	if err != nil {
		logrus.Errorf("SuiGetLatestCheckpointSequenceNumber failed, err: %v", err)
		return 0, err
	}
	return latestCheckpoint, nil
}

func (s *SuiRpcClient) SuiGetObject(ctx context.Context, req models.SuiGetObjectRequest) (models.SuiObjectResponse, error) {
	objectData, err := s.client.SuiGetObject(ctx, req)
	if err != nil {
		logrus.Errorf("SuiGetObjectData failed, err: %v", err)
		return models.SuiObjectResponse{}, err
	}
	return objectData, nil
}

func (s *SuiRpcClient) SuiGetTotalSupply(ctx context.Context, req models.SuiXGetTotalSupplyRequest) (models.TotalSupplyResponse, error) {
	totalSupply, err := s.client.SuiXGetTotalSupply(ctx, req)
	if err != nil {
		logrus.Errorf("SuiGetTotalSupply failed, err: %v", err)
		return models.TotalSupplyResponse{}, err
	}
	return totalSupply, nil
}

func (s *SuiRpcClient) SuiXGetBalance(ctx context.Context, req models.SuiXGetBalanceRequest) (models.CoinBalanceResponse, error) {
	balance, err := s.client.SuiXGetBalance(ctx, req)
	if err != nil {
		logrus.Errorf("SuiXGetBalance failed, err: %v", err)
		return models.CoinBalanceResponse{}, err
	}
	return balance, nil
}

func (s *SuiRpcClient) SuiGetTransactionBlock(ctx context.Context, req models.SuiGetTransactionBlockRequest) (models.SuiTransactionBlockResponse, error) {
	transactionBlock, err := s.client.SuiGetTransactionBlock(ctx, req)
	if err != nil {
		logrus.Errorf("SuiGetTransactionBlock failed, err: %v", err)
		return models.SuiTransactionBlockResponse{}, err
	}
	return transactionBlock, nil
}

func (s *SuiRpcClient) SuiMultiGetTransactionBlocks(ctx context.Context, req models.SuiMultiGetTransactionBlocksRequest) (models.SuiMultiGetTransactionBlocksResponse, error) {
	transactionBlocks, err := s.client.SuiMultiGetTransactionBlocks(ctx, req)
	if err != nil {
		logrus.Errorf("SuiMultiGetTransactionBlocks failed, err: %v", err)
		return models.SuiMultiGetTransactionBlocksResponse{}, err
	}
	return transactionBlocks, nil
}

func (s *SuiRpcClient) SuiGetCheckpoint(ctx context.Context, req models.SuiGetCheckpointRequest) (models.CheckpointResponse, error) {
	checkpoint, err := s.client.SuiGetCheckpoint(ctx, req)
	if err != nil {
		logrus.Errorf("SuiGetCheckpoint failed, err: %v", err)
		return models.CheckpointResponse{}, err
	}
	return checkpoint, nil
}

func (s *SuiRpcClient) SuiGetCheckpoints(ctx context.Context, req models.SuiGetCheckpointsRequest) (models.PaginatedCheckpointsResponse, error) {
	checkpoints, err := s.client.SuiGetCheckpoints(ctx, req)
	if err != nil {
		logrus.Errorf("SuiGetCheckpoints failed, err: %v", err)
		return models.PaginatedCheckpointsResponse{}, err
	}
	return checkpoints, nil
}

func (s *SuiRpcClient) SuiXGetCoinMetadata(ctx context.Context, req models.SuiXGetCoinMetadataRequest) (models.CoinMetadataResponse, error) {
	coinMetadata, err := s.client.SuiXGetCoinMetadata(ctx, req)
	if err != nil {
		logrus.Errorf("SuiXGetCoinMetadata failed, err: %v", err)
		return models.CoinMetadataResponse{}, err
	}
	return coinMetadata, nil
}
