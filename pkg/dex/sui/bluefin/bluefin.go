package bluefin

import (
	"chain-parse-service/service/types"
	"context"

	mapset "github.com/deckarep/golang-set"
)

const dexName = "Bluefin"

type Bluefin struct {
	Name     string
	EventSet mapset.Set
	Factory  string
}

func NewBluefin() *Bluefin {
	return &Bluefin{
		Name: dexName,
		EventSet: mapset.NewSetFromSlice([]interface{}{
			BluefinAddLiquidityEventType,
			BluefinRemoveLiquidityEventType,
			BluefinAssetSwapEventType,
			BluefinFlashSwapEventType,
		}),
		Factory: BluefinAmmAddr,
	}
}

func (b *Bluefin) GetName() string {
	return b.Name
}

func (b *Bluefin) Events() mapset.Set {
	return b.EventSet
}

func (b *Bluefin) AddFactory(factory string) {
	b.Factory = factory
}

func (b *Bluefin) ParseLog(ctx context.Context, chain *types.Chain, block *types.Block, tx *types.Transaction, eventLog *types.SuiEventLog) (p *types.Pool, tokens []*types.Token, transactions []*types.Transaction, reserve *types.Reserve, liquidity []*types.Liquidity, err error) {
	return nil, nil, nil, nil, nil, nil
}

func (b *Bluefin) ParseSuiLog(ctx context.Context, chain *types.Chain, block *types.SuiBlock, tx *types.SuiTransaction, eventLog *types.SuiEventLog) (p *types.Pool, tokens []*types.Token, transactions []*types.Transaction, reserve *types.Reserve, liquidity []*types.Liquidity, err error) {
	return nil, nil, nil, nil, nil, nil
}
