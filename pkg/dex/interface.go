package dex

import (
	"chain-parse-service/service/types"
	"context"

	mapset "github.com/deckarep/golang-set"
)

type BaseDexParser interface {
	AddFactory(factory string)
	ParseLog(ctx context.Context, chain *types.Chain, block *types.Block, tx *types.Transaction, eventLog *types.EventLog) (p *types.Pool, tokens []*types.Token, transactions []*types.Transaction, reserve *types.Reserve, liquidity []*types.Liquidity, err error)
	GetName() string
	Events() mapset.Set
}

type SuiDexParser interface {
	BaseDexParser
	ParseSuiLog(ctx context.Context, chain *types.Chain, block *types.SuiBlock, tx *types.SuiTransaction, eventLog *types.SuiEventLog) (p *types.Pool, tokens []*types.Token, transactions []*types.Transaction, reserve *types.Reserve, liquidity []*types.Liquidity, err error)
}

type SolDexParser interface {
	BaseDexParser
	// ParseSolLog(ctx context.Context, chain *types.Chain, block *types.SolBlock, tx *types.SolTransaction, eventLog *types.SolEventLog) (p *types.Pool, tokens []*types.Token, transactions []*types.Transaction, reserve *types.Reserve, liquidity []*types.Liquidity, err error)
}
